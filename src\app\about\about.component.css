/* Remove duplicate imports and styles - these are handled globally in styles.css */



.montserrat {
  font-family: "Montserrat", sans-serif;
}

img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.container {
  max-width: 1300px;
  margin: 0px auto 0px auto;
  padding: 0px 40px;
}
@media (min-width: 1200px) and (max-width: 1441px) {
  .container {
    max-width: 1250px;
    padding: 0px 36px;
  }
}
@media (max-width: 767px) {
  .container {
    padding: 0px 30px;
  }
}
@media (max-width: 479px) {
  .container {
    padding: 0px 30px 0px 20px;
  }
}

.about-me {
  position: relative;
  overflow: hidden;
  padding: 0; /* Remove default padding to allow full-width iframe */
}
.about-me .about-me-container {
  position: relative;
}
@media (max-width: 960px) {
  .about-me .about-me-container {
    padding-bottom: 100px;
  }
}
.about-me .about-me-container .about-me-title {
  font-size: 3.5rem; /* 56px */
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8); /* Subtle white shadow for better readability */
}

@media (max-width: 768px) {
  .about-me .about-me-container .about-me-title {
    font-size: 2.5rem; /* 40px */
    margin-bottom: 25px;
  }
}

@media (max-width: 500px) {
  .about-me .about-me-container .about-me-title {
    font-size: 2rem; /* 32px */
    margin-bottom: 20px;
  }
}

.about-me-flex-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  text-align: center;
}

@media (max-width: 768px) {
  .about-me-flex-container {
    gap: 25px;
  }
}

@media (max-width: 500px) {
  .about-me-flex-container {
    gap: 20px;
  }
}
.about-me-flex-container .about-me-image {
  position: relative;
  width: 400px;
  height: 400px;
}
@media (max-width: 500px) {
  .about-me-flex-container .about-me-image {
    width: 300px;
    height: 300px;
  }
}
.about-me-flex-container .about-me-image .back-div {
  position: absolute;
  bottom: 0;
  z-index: -3;
  background-color: var(--accent-pink);
  width: 80%;
  height: 80%;
}
/* Title font family removed - using global Inter font system */
.about-me-flex-container .about-me-image .black-image {
  z-index: -2;
  position: absolute;
  left: 10px;
  bottom: 10px;
  height: 100%;
}
.about-me-flex-container .about-me-image .black-image img {
  height: 100%;
}
.about-me-flex-container .about-me-image .main-image {
  width: 75%;
  height: 75%;
  overflow: hidden;
  position: absolute;
  right: 15%;
  top: 15%;
  box-shadow: var(--primary-pink-light) 0px 7px 50px 0px;
  transition: all 0.2s ease-out;
}
.about-me-flex-container .about-me-image .main-image:hover {
  transform-origin: top center;
  transform: scale(1.5);
  border-radius: 25px;
}
.about-me-flex-container .about-me-image .main-image img {
  transform-origin: center center;
  transform: scale(2);
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.2s ease-out;
}
.about-me-flex-container .about-me-image .main-image img:hover {
  transform: scale(1);
}
.about-me-flex-container .about-me-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  width: 100%;
  max-width: 800px;
}

@media (max-width: 768px) {
  .about-me-flex-container .about-me-content {
    gap: 25px;
    max-width: 100%;
  }
}
.about-me-flex-container .about-me-content .logo {
  max-width: 200px;
}
.about-me-flex-container .about-me-content .logo img {
  filter: drop-shadow(0 0 25px rgb(0, 0, 0));
}
@media (max-width: 500px) {
  .about-me-flex-container .about-me-content .logo img {
    transform: rotateZ(90deg);
  }
}
.about-me-flex-container .about-me-content .text {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1.125rem; /* 18px */
  line-height: 1.7;
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .about-me-flex-container .about-me-content .text {
    font-size: 1rem; /* 16px */
    line-height: 1.6;
    max-width: 100%;
  }
}

@media (max-width: 500px) {
  .about-me-flex-container .about-me-content .text {
    font-size: 0.9rem; /* 14px */
    line-height: 1.6;
  }
}


.spline-container {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  height: 400px;
}

spline-viewer {
  width: 100%;
  height: 100%;
  border: none;
}

.about-me-content {
  flex: 2;
  min-width: 300px;
}

.cta:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-radius: 50px;
  background: var(--primary-pink-lighter);
  width: 45px;
  height: 45px;
  transition: all 0.3s ease;
}

.cta span {
  position: relative;
  font-family: "Ubuntu", sans-serif;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
}

.cta svg {
  position: relative;
  top: 0;
  margin-left: 10px;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke: var(--text-secondary);
  stroke-width: 2;
  transform: translateX(-5px);
  transition: all 0.3s ease;
}

.cta:hover:before {
  width: 100%;
  background: var(--primary-pink-lighter);
}

.cta:hover svg {
  transform: translateX(0);
}

.cta:active {
  transform: scale(0.95);
}
.about-me-flex-container {
  display: flex;
  gap: 20px;
}

.about-me-image {
  position: relative;
}

.back-image img {
  width: 300px;
  height: auto;
  filter: blur(10px);
  transition: filter 0.5s ease-in-out, transform 0.5s ease-in-out;
}

.front-image img {
  width: 300px;
  height: auto;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.about-me-image .show-front-image img {
  filter: blur(0);
  transform: scale(1.05); /* Slight zoom when clicked */
}

.cta {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 15px 30px;
  background: linear-gradient(135deg, var(--primary-pink-lighter), var(--primary-pink-light));
  border: 2px solid var(--border-pink);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 15px rgba(219, 39, 119, 0.2);
}

.cta:hover {
  background: linear-gradient(135deg, var(--primary-pink-light), var(--accent-pink));
  border-color: var(--primary-pink);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(219, 39, 119, 0.3);
}

/* Duplicate text style removed - handled in .about-me-flex-container .about-me-content .text */
/* Spline 3D Animation Background */
.spline-fullscreen-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw; /* Full viewport width */
  height: 500px;
  z-index: 1; /* Behind content */
  margin-left: calc(-50vw + 50%); /* Center and extend to full width */
}

.spline-fullscreen-wrapper iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

/* Content Overlay */
.content-overlay {
  position: relative;
  z-index: 2; /* Above iframe */
  background: rgba(255, 255, 255, 0.9); /* Semi-transparent background for better text readability */
  -webkit-backdrop-filter: blur(2px); /* Safari support */
  backdrop-filter: blur(2px); /* Subtle blur effect for better text contrast */
  border-radius: 20px;
  margin: 50px auto;
  max-width: 1200px;
  padding: 60px 40px;
  box-shadow: 0 10px 30px rgba(219, 39, 119, 0.1); /* Subtle pink shadow */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .spline-fullscreen-wrapper {
    height: 400px;
  }

  .content-overlay {
    margin: 30px 20px;
    padding: 40px 30px;
    border-radius: 15px;
  }
}

@media (max-width: 480px) {
  .spline-fullscreen-wrapper {
    height: 350px;
  }

  .content-overlay {
    margin: 20px 15px;
    padding: 30px 20px;
    border-radius: 12px;
  }
}
